import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient } from "@aws-sdk/lib-dynamodb";

let dynamoClient: DynamoDBDocumentClient | null = null;

export const MERCHANT_TABLE_NAME = process.env.MERCHANT_DATA_TABLE_NAME || "";

const clientConfig: { region: string } = {
  region: process.env.AWS_REGION || "us-east-1",
  // profile: process.env.STAGE && process.env.STAGE === "dev" ? "payrix" : "default",
};

export function getMerchantDataClient(): DynamoDBDocumentClient {
  if (!dynamoClient) {
    const client = new DynamoDBClient({
      region: clientConfig?.region,
    });
    dynamoClient = DynamoDBDocumentClient.from(client, {
      marshallOptions: {
        removeUndefinedValues: true,
      },
    });
  }
  return dynamoClient;
}
